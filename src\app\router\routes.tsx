import LoginPage from "auth/pages";
import GettingStartedPage from "gettingStarted/pages";
import MapPage from "map/pages";

interface IRoute {
  path: string;
  element: React.ReactNode;
}

interface IRoutes extends IRoute {
  children?: IRoute[];
}

const INIT_ROUTES = [
  { path: "/map", element: <MapPage /> },
  { path: "/getting-started", element: <GettingStartedPage /> },
];

const AUTH_ROUTES: IRoutes[] = [{ path: "/login", element: <LoginPage /> }];

export default { INIT_ROUTES, AUTH_ROUTES };
