import "ol/ol.css";
import { useEffect, useRef } from "react";
import Map from "ol/Map";
import View from "ol/View";
import WebGLTileLayer from "ol/layer/WebGLTile.js";
import DataTileSource from "ol/source/DataTile.js";
import { fromLonLat } from "ol/proj.js";

const OLMap = () => {
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // 1) WebGL-слой для OSM с DataTileSource
    const webglLayer = new WebGLTileLayer({
      source: new DataTileSource({
        url: "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
        maxZoom: 19,
        loader: async (z, x, y) => {
          const response = await fetch(
            `https://tile.openstreetmap.org/${z}/${x}/${y}.png`
          );
          const blob = await response.blob();
          const imageBitmap = await createImageBitmap(blob);

          // Создаем canvas для получения данных пикселей
          const canvas = new OffscreenCanvas(256, 256);
          const ctx = canvas.getContext("2d")!;
          ctx.drawImage(imageBitmap, 0, 0);

          // Получаем данные пикселей как Uint8Array
          const imageData = ctx.getImageData(0, 0, 256, 256);
          return new Uint8Array(imageData.data.buffer);
        },
      }),
    });

    // 2) Инициализируем карту
    const map = new Map({
      target: mapRef.current,
      layers: [osmLayer],
      view: new View({
        center: fromLonLat([72.618343, 40.265467]), // Бишкек
        zoom: 12,
      }),
    });

    return () => {
      map.setTarget(undefined);
    };
  }, []);

  return <div ref={mapRef} style={{ width: "100%", height: "100vh" }} />;
};

export default OLMap;
