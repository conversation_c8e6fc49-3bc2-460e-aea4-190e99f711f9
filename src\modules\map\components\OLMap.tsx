import "ol/ol.css";
import { useEffect, useRef } from "react";
import Map from "ol/Map";
import View from "ol/View";
import WebGLTileLayer from "ol/layer/WebGLTile.js";
import OSM from "ol/source/OSM.js";
import { fromLonLat } from "ol/proj.js";

const OLMap = () => {
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // 1) WebGL-слой для OSM
    const webglLayer = new WebGLTileLayer({
      source: new OSM(),
    });

    // 2) Инициализируем карту
    const map = new Map({
      target: mapRef.current,
      layers: [webglLayer],
      view: new View({
        center: fromLonLat([72.618343, 40.265467]), // Бишкек
        zoom: 12,
      }),
    });

    return () => {
      map.setTarget(undefined);
    };
  }, []);

  return <div ref={mapRef} style={{ width: "100%", height: "100vh" }} />;
};

export default OLMap;
