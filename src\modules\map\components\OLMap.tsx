import "ol/ol.css";
import { useEffect, useRef } from "react";
import Map from "ol/Map.js";
import View from "ol/View.js";
import TileLayer from "ol/layer/Tile.js";
import WebGLVectorLayer from "ol/layer/WebGLVector.js";
import OSM from "ol/source/OSM.js";
import VectorSource from "ol/source/Vector.js";
import GeoJSON from "ol/format/GeoJSON.js";
import { fromLonLat } from "ol/proj.js";

const OLMap = () => {
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // 1) Базовый OSM слой
    const osmLayer = new TileLayer({
      source: new OSM(),
    });

    // 2) WebGL векторный слой для демонстрации WebGL возможностей
    const webglVectorLayer = new WebGLVectorLayer({
      source: new VectorSource({
        url: "https://openlayers.org/data/vector/ecoregions.json",
        format: new GeoJSON(),
      }),
      style: {
        "stroke-color": [51, 153, 204, 1], // #3399CC в формате RGBA
        "stroke-width": 2,
        "fill-color": [255, 255, 255, 0.3], // Белый с прозрачностью
      },
    });

    // 3) Инициализируем карту
    const map = new Map({
      target: mapRef.current,
      layers: [osmLayer, webglVectorLayer],
      view: new View({
        center: fromLonLat([72.618343, 40.265467]), // Бишкек
        zoom: 6, // Уменьшаем зум, чтобы видеть векторные данные
      }),
    });

    return () => {
      map.setTarget(undefined);
    };
  }, []);

  return <div ref={mapRef} style={{ width: "100%", height: "100vh" }} />;
};

export default OLMap;
