import "ol/ol.css";
import { useEffect, useRef, useState } from "react";
import Map from "ol/Map.js";
import View from "ol/View.js";
import TileLayer from "ol/layer/Tile.js";
import WebGLVectorLayer from "ol/layer/WebGLVector.js";
import OSM from "ol/source/OSM.js";
import VectorSource from "ol/source/Vector.js";
import GeoJSON from "ol/format/GeoJSON.js";
import { fromLonLat } from "ol/proj.js";

const OLMap = () => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [webglInfo, setWebglInfo] = useState<string>("");

  useEffect(() => {
    if (!mapRef.current) return;

    // 1) WebGL векторный тайловый слой с простым стилем
    const webglVectorTileLayer = new WebGLVectorTileLayer({
      source: new VectorTileSource({
        // Используем публичные векторные тайлы OpenStreetMap
        url: "https://basemaps.arcgis.com/arcgis/rest/services/OpenStreetMap_v2/VectorTileServer/tile/{z}/{y}/{x}.pbf",
        format: new MVT(),
        maxZoom: 15,
      }),
      // Простой стиль для всех объектов
      style: {
        "stroke-color": [100, 150, 200, 0.8],
        "stroke-width": 1,
        "fill-color": [200, 220, 240, 0.3],
      },
    });

    // 2) Инициализируем карту (100% WebGL!)
    const map = new Map({
      target: mapRef.current,
      layers: [webglVectorTileLayer],
      view: new View({
        center: fromLonLat([72.618343, 40.265467]), // Бишкек
        zoom: 12,
      }),
    });

    // 3) Проверяем, что используется WebGL
    setTimeout(() => {
      const canvas = mapRef.current?.querySelector("canvas");
      if (canvas) {
        const webglContext =
          canvas.getContext("webgl") || canvas.getContext("webgl2");
        if (webglContext) {
          const info = `✅ WebGL активен! GPU: ${webglContext.getParameter(
            webglContext.RENDERER
          )}`;
          console.log(info);
          setWebglInfo(info);
        } else {
          const info = "❌ WebGL не активен, используется Canvas 2D";
          console.log(info);
          setWebglInfo(info);
        }
      }
    }, 1000);

    return () => {
      map.setTarget(undefined);
    };
  }, []);

  return (
    <div style={{ position: "relative", width: "100%", height: "100vh" }}>
      <div ref={mapRef} style={{ width: "100%", height: "100%" }} />
      {webglInfo && (
        <div
          style={{
            position: "absolute",
            top: "10px",
            left: "10px",
            background: "rgba(0, 0, 0, 0.8)",
            color: "white",
            padding: "8px 12px",
            borderRadius: "4px",
            fontSize: "12px",
            fontFamily: "monospace",
            zIndex: 1000,
          }}
        >
          {webglInfo}
        </div>
      )}
    </div>
  );
};

export default OLMap;
