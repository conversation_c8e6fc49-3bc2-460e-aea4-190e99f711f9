import "ol/ol.css";
import { useEffect, useRef } from "react";
import Map from "ol/Map.js";
import View from "ol/View.js";
import WebGLTileLayer from "ol/layer/WebGLTile.js";
import DataTileSource from "ol/source/DataTile.js";
import { fromLonLat } from "ol/proj.js";

const OLMap = () => {
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // 1) Чистый WebGL-слой для OSM (без Canvas 2D!)
    const webglLayer = new WebGLTileLayer({
      source: new DataTileSource({
        loader: async (z: number, x: number, y: number) => {
          try {
            // Загружаем OSM тайл
            const response = await fetch(
              `https://tile.openstreetmap.org/${z}/${x}/${y}.png`
            );
            const arrayBuffer = await response.arrayBuffer();

            // Возвращаем сырые данные изображения для WebGL
            // WebGL сам декодирует PNG/JPEG через GPU
            return new Uint8Array(arrayBuffer);
          } catch (error) {
            // В случае ошибки возвращаем пустой массив
            console.warn(`Failed to load tile ${z}/${x}/${y}:`, error);
            return new Uint8Array(0);
          }
        },
        // Указываем, что данные - это изображение
        bandCount: 4, // RGBA
        interpolate: false, // Четкие пиксели без сглаживания
      }),
      // Добавляем WebGL стили для оптимизации
      style: {
        color: ["band", 1], // Используем первый канал (RGB)
      },
    });

    // 2) Инициализируем карту
    const map = new Map({
      target: mapRef.current,
      layers: [webglLayer],
      view: new View({
        center: fromLonLat([72.618343, 40.265467]), // Бишкек
        zoom: 12,
      }),
    });

    return () => {
      map.setTarget(undefined);
    };
  }, []);

  return <div ref={mapRef} style={{ width: "100%", height: "100vh" }} />;
};

export default OLMap;
