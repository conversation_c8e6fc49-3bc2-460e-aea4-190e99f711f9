import "ol/ol.css";
import { useEffect, useRef } from "react";
import Map from "ol/Map.js";
import View from "ol/View.js";
import WebGLVectorTileLayer from "ol/layer/WebGLVectorTile.js";
import VectorTileSource from "ol/source/VectorTile.js";
import MVT from "ol/format/MVT.js";
import { fromLonLat } from "ol/proj.js";

const OLMap = () => {
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // 1) WebGL векторный тайловый слой (полностью GPU рендеринг!)
    const webglVectorTileLayer = new WebGLVectorTileLayer({
      source: new VectorTileSource({
        // Используем OpenMapTiles - бесплатные векторные тайлы
        url: "https://api.maptiler.com/tiles/v3/{z}/{x}/{y}.pbf?key=get_your_own_OpIi9ZULNHzrESv6T2vL",
        format: new MVT(),
        maxZoom: 14,
      }),
      style: {
        // Стиль для дорог
        "stroke-color": [
          "case",
          ["==", ["get", "class"], "motorway"],
          [255, 100, 100, 1], // Красные автомагистрали
          ["==", ["get", "class"], "primary"],
          [255, 200, 100, 1], // Оранжевые основные дороги
          [200, 200, 200, 0.8], // Серые остальные дороги
        ],
        "stroke-width": [
          "case",
          ["==", ["get", "class"], "motorway"],
          4, // Толстые автомагистрали
          ["==", ["get", "class"], "primary"],
          3, // Средние основные дороги
          1, // Тонкие остальные дороги
        ],
        // Стиль для зданий
        "fill-color": [
          "case",
          ["==", ["get", "layer"], "building"],
          [150, 150, 150, 0.8], // Серые здания
          [100, 200, 100, 0.3], // Зеленые парки/природа
        ],
      },
    });

    // 2) Инициализируем карту (100% WebGL!)
    const map = new Map({
      target: mapRef.current,
      layers: [webglVectorTileLayer],
      view: new View({
        center: fromLonLat([72.618343, 40.265467]), // Бишкек
        zoom: 12,
      }),
    });

    return () => {
      map.setTarget(undefined);
    };
  }, []);

  return <div ref={mapRef} style={{ width: "100%", height: "100vh" }} />;
};

export default OLMap;
