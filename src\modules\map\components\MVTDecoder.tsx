import "ol/ol.css";
import { useState } from "react";
import MVT from "ol/format/MVT";

const MVTDecoder = () => {
  const [featuresList, setFeaturesList] = useState([]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;

    if (!files) return;

    const file = files[0];
    if (!file) return;

    console.log(file);

    const reader = new FileReader();
    reader.onload = () => {
      const arrayBuffer = reader.result;
      // Инициализируем декодер формата MVT
      const mvtFormat = new MVT();
      // Обратите внимание: extent указан для Web Mercator (EPSG:3857)
      const features = mvtFormat.readFeatures(arrayBuffer as any, {
        extent: [-20037508.34, -20037508.34, 20037508.34, 20037508.34],
        featureProjection: "EPSG:3857",
      });
      console.log("Декодированные фичи:", features);
      setFeaturesList(features as any);
    };
    reader.readAsArrayBuffer(file);
  };

  console.log(featuresList);

  return (
    <div>
      <input type="file" onChange={handleFileChange} />
      <div>
        {featuresList.length > 0 ? (
          <ul>
            {featuresList.map((feature, index) => (
              <li key={index}>
                Фича {index + 1}: {(feature as any).getId() || "без ID"}
              </li>
            ))}
          </ul>
        ) : (
          <p>Фичи не загружены</p>
        )}
      </div>
    </div>
  );
};

export default MVTDecoder;
